import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { VideoTrack, isTrackReference } from '@livekit/components-react';
import { Avatar } from 'antd';

// Custom PiP Tile Component - using LiveKit VideoTrack like ParticipantTile
function CustomPipTile({ trackRef, pipWindow }) {
  const [windowSize, setWindowSize] = useState({ width: 320, height: 240 });

  // Monitor PiP window size changes for dynamic resizing
  useEffect(() => {
    if (!pipWindow) return;

    const updateSize = () => {
      const newSize = {
        width: pipWindow.innerWidth,
        height: pipWindow.innerHeight
      };
      setWindowSize(newSize);
    };

    // Initial size
    updateSize();

    // Listen for resize events
    pipWindow.addEventListener('resize', updateSize);

    return () => {
      pipWindow.removeEventListener('resize', updateSize);
    };
  }, [pipWindow]);

  // Get participant info
  const participant = trackRef?.participant;
  const participantName = participant?.name || participant?.identity || 'You';
  const avatarText = participantName.charAt(0).toUpperCase();

  // Calculate dynamic sizing with borders and aspect ratio preservation
  const borderSize = Math.max(8, Math.min(20, Math.min(windowSize.width, windowSize.height) * 0.05));
  const containerWidth = windowSize.width - (borderSize * 2);
  const containerHeight = windowSize.height - (borderSize * 2);

  // Calculate video dimensions with forced 16:9 aspect ratio - always maintain 16:9 even if smaller
  const calculateVideoDimensions = () => {
    const targetAspectRatio = 16 / 9; // Force 16:9 aspect ratio
    const containerAspectRatio = containerWidth / containerHeight;

    let calculatedWidth;
    let calculatedHeight;

    if (targetAspectRatio > containerAspectRatio) {
      // Container is taller than 16:9 - fit video to container width, video will be smaller in height
      calculatedWidth = containerWidth;
      calculatedHeight = calculatedWidth / targetAspectRatio;
    } else {
      // Container is wider than 16:9 - fit video to container height, video will be smaller in width
      calculatedHeight = containerHeight;
      calculatedWidth = calculatedHeight * targetAspectRatio;
    }

    return {
      width: Math.round(calculatedWidth),
      height: Math.round(calculatedHeight)
    };
  };

  const videoDimensions = calculateVideoDimensions();

  return (
    <div style={{
      width: '100%',
      height: '100%',
      background: '#000',
      padding: `${borderSize}px`,
      boxSizing: 'border-box'
    }}>
      {/* Video container with proper aspect ratio and centering */}
      <div style={{
        width: `${containerWidth}px`,
        height: `${containerHeight}px`,
        position: 'relative',
        backgroundColor: '#1a1a1a',
        borderRadius: '8px',
        border: '2px solid #2196F3',
        overflow: 'hidden'
      }}>
        {/* Check if we should show video or avatar */}
        {isTrackReference(trackRef) &&
        (trackRef.publication?.kind === "video" ||
          trackRef.source === Track.Source.Camera ||
          trackRef.source === Track.Source.ScreenShare) &&
        trackRef.publication?.track &&
        !trackRef.publication?.isMuted &&
        trackRef.publication?.isSubscribed ? (
          /* Show VideoTrack when camera is on and not muted */
          <VideoTrack
            trackRef={trackRef}
            style={{
              width: `${videoDimensions.width}px`,
              height: `${videoDimensions.height}px`,
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              objectFit: 'contain',
              backgroundColor: 'transparent'
            }}
          />
        ) : (
          /* Show Avatar when camera is off/muted or no video track */
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            textAlign: 'center'
          }}>
            <Avatar
              style={{
                width: Math.max(40, Math.min(80, Math.min(containerWidth, containerHeight) * 0.3)),
                height: Math.max(40, Math.min(80, Math.min(containerWidth, containerHeight) * 0.3)),
                backgroundColor: '#2196F3',
                fontSize: Math.max(16, Math.min(32, Math.min(containerWidth, containerHeight) * 0.15)),
                fontWeight: 'bold',
                border: '3px solid #fff'
              }}
            >
              {avatarText}
            </Avatar>
            <div style={{
              fontSize: Math.max(12, Math.min(18, Math.min(containerWidth, containerHeight) * 0.08)),
              maxWidth: '90%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              marginTop: '8px'
            }}>
              {participantName}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export function usePictureInPicture({
  room,
  tracks,
  isTrackReference: isTrackReferenceCheck,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Simple configuration
  const defaultConfig = useMemo(() => ({
    width: 320,
    height: 240
  }), []);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Get current track to display - screen share first, then camera
  const currentTrack = useMemo(() => {
    if (!tracks?.length) {
      return null;
    }

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReferenceCheck)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      return screenShareTracks[0];
    }

    // Priority 2: Local camera
    const localCameraTracks = tracks
      .filter(isTrackReferenceCheck)
      .filter((track) =>
        track.publication.source === Track.Source.Camera &&
        track.participant.isLocal
      );

    if (localCameraTracks.length > 0) {
      return localCameraTracks[0];
    }

    return null;
  }, [tracks, isTrackReferenceCheck, room]);

  // Custom PiP Content using responsive tile
  const PipContent = useCallback(() => {
    if (!currentTrack) {
      // Fallback when no tracks available - create a mock track for local participant
      const mockTrack = {
        participant: room?.localParticipant,
        publication: null
      };
      return <CustomPipTile trackRef={mockTrack} pipWindow={pipWindowRef.current} />;
    }

    return <CustomPipTile trackRef={currentTrack} pipWindow={pipWindowRef.current} />;
  }, [currentTrack, room]);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Simple styles
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100%;
      height: 100%;
      background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  `, []);

  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported
  };
}