import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import {
  ParticipantContext,
  TrackRefContext
} from '@livekit/components-react';
import { ParticipantTile } from './ParticipantTile';

// LiveKit PiP Tile Component - using custom ParticipantTile
function LiveKitPipTile({ trackRef, pipWindow }) {
  const [windowSize, setWindowSize] = useState({ width: 320, height: 240 });

  // Monitor PiP window size changes for dynamic resizing
  useEffect(() => {
    if (!pipWindow) return;

    const updateSize = () => {
      const newSize = {
        width: pipWindow.innerWidth,
        height: pipWindow.innerHeight
      };
      setWindowSize(newSize);
    };

    // Initial size
    updateSize();

    // Listen for resize events
    pipWindow.addEventListener('resize', updateSize);

    return () => {
      pipWindow.removeEventListener('resize', updateSize);
    };
  }, [pipWindow]);

  // Calculate dynamic sizing with borders and 16:9 aspect ratio
  const borderSize = Math.max(8, Math.min(20, Math.min(windowSize.width, windowSize.height) * 0.05));
  const availableWidth = windowSize.width - (borderSize * 2);
  const availableHeight = windowSize.height - (borderSize * 2);

  // Calculate 16:9 aspect ratio dimensions that fit within available space
  const targetAspectRatio = 16 / 9;
  const availableAspectRatio = availableWidth / availableHeight;

  let videoWidth;
  let videoHeight;

  if (availableAspectRatio > targetAspectRatio) {
    // Available space is wider than 16:9, fit to height
    videoHeight = availableHeight;
    videoWidth = videoHeight * targetAspectRatio;
  } else {
    // Available space is taller than 16:9, fit to width
    videoWidth = availableWidth;
    videoHeight = videoWidth / targetAspectRatio;
  }

  return (
    <div style={{
      width: '100%',
      height: '100%',
      background: '#000',
      padding: `${borderSize}px`,
      boxSizing: 'border-box',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {/* Container for LiveKit ParticipantTile with 16:9 aspect ratio */}
      <div style={{
        width: `${videoWidth}px`,
        height: `${videoHeight}px`,
        position: 'relative',
        backgroundColor: '#1a1a1a',
        borderRadius: '8px',
        border: '2px solid #2196F3',
        overflow: 'hidden'
      }}>
        {/* Use custom ParticipantTile with proper context */}
        <ParticipantContext.Provider value={trackRef?.participant}>
          <TrackRefContext.Provider value={trackRef}>
            <ParticipantTile
              trackRef={trackRef}
              style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
                top: 0,
                left: 0
              }}
              className="pip-participant-tile"
            />
          </TrackRefContext.Provider>
        </ParticipantContext.Provider>
      </div>
    </div>
  );
}

export function usePictureInPicture({
  room,
  tracks,
  isTrackReference: isTrackReferenceCheck,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Simple configuration
  const defaultConfig = useMemo(() => ({
    width: 320,
    height: 240
  }), []);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Get current track to display - screen share first, then camera
  const currentTrack = useMemo(() => {
    if (!tracks?.length) {
      return null;
    }

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReferenceCheck)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      return screenShareTracks[0];
    }

    // Priority 2: Local camera
    const localCameraTracks = tracks
      .filter(isTrackReferenceCheck)
      .filter((track) =>
        track.publication.source === Track.Source.Camera &&
        track.participant.isLocal
      );

    if (localCameraTracks.length > 0) {
      return localCameraTracks[0];
    }

    return null;
  }, [tracks, isTrackReferenceCheck, room]);

  // LiveKit PiP Content using custom ParticipantTile
  const PipContent = useCallback(() => {
    if (!currentTrack) {
      // Show a simple message when no tracks are available instead of trying to render ParticipantTile
      return (
        <div style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '16px',
          textAlign: 'center',
          padding: '20px'
        }}>
          No video available
        </div>
      );
    }

    return <LiveKitPipTile trackRef={currentTrack} pipWindow={pipWindowRef.current} />;
  }, [currentTrack, room]);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Enhanced styles for LiveKit ParticipantTile
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100%;
      height: 100%;
      background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* LiveKit ParticipantTile styles */
    .lk-participant-tile {
      width: 100% !important;
      height: 100% !important;
      border-radius: 8px;
      overflow: hidden;
      position: relative;
    }

    .lk-participant-tile video {
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
      object-position: center !important;
      transform: none !important;
      max-width: 100% !important;
      max-height: 100% !important;
    }

    /* Override any LiveKit default video styles that might cause zooming */
    [data-lk-participant-tile] video {
      object-fit: contain !important;
      transform: none !important;
    }

    /* Ensure video track container doesn't cause zoom */
    .lk-video-track {
      width: 100% !important;
      height: 100% !important;
    }

    .lk-video-track video {
      object-fit: contain !important;
      width: 100% !important;
      height: 100% !important;
    }

    /* Custom PiP participant tile styles with 16:9 aspect ratio */
    .pip-participant-tile {
      width: 100% !important;
      height: 100% !important;
      aspect-ratio: 16/9 !important;
    }

    .pip-participant-tile video {
      object-fit: cover !important;
      object-position: center !important;
      width: 100% !important;
      height: 100% !important;
      transform: none !important;
      scale: none !important;
      aspect-ratio: 16/9 !important;
    }

    /* Ensure video fills the 16:9 container properly */
    .pip-participant-tile .lk-video-track {
      width: 100% !important;
      height: 100% !important;
      aspect-ratio: 16/9 !important;
    }

    .pip-participant-tile .lk-video-track video {
      object-fit: cover !important;
      aspect-ratio: 16/9 !important;
    }

    .lk-participant-placeholder {
      width: 100% !important;
      height: 100% !important;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1a1a1a;
    }

    .lk-participant-name {
      position: absolute;
      bottom: 8px;
      left: 8px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
  `, []);

  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported
  };
}